# jvQuant WebSocket??
jvquant.websocket.url=ws://your-server-address/?token=your-token
jvquant.websocket.reconnect.interval=5000
jvquant.websocket.max.reconnect.attempts=10

# ???? - ????????????????
jvquant.subscription.symbols=600519,000001,000002
jvquant.subscription.types=lv1,lv2,lv10

# PostgreSQL?????
database.url=********************************************
database.username=postgres
database.password=password
database.driver=org.postgresql.Driver

# ?????
database.pool.maximum.size=20
database.pool.minimum.idle=5
database.pool.connection.timeout=30000
database.pool.idle.timeout=600000
database.pool.max.lifetime=1800000

# ????
logging.level.root=INFO
logging.level.com.quantitative=DEBUG
