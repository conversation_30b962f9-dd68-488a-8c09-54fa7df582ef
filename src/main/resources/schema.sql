-- 创建数据库（如果不存在）
-- CREATE DATABASE market_data;

-- 基础行情表 (lv1)
CREATE TABLE IF NOT EXISTS lv1_market_data (
    id BIGSERIAL PRIMARY KEY,
    symbol VARCHAR(20) NOT NULL,
    symbol_name VARCHAR(100),
    push_time TIMESTAMP NOT NULL,
    latest_price DECIMAL(10,3),
    change_percent DECIMAL(8,4),
    turnover BIGINT,
    volume BIGINT,
    -- 买五档
    bid1_volume BIGINT,
    bid1_price DECIMAL(10,3),
    bid2_volume BIGINT,
    bid2_price DECIMAL(10,3),
    bid3_volume BIGINT,
    bid3_price DECIMAL(10,3),
    bid4_volume BIGINT,
    bid4_price DECIMAL(10,3),
    bid5_volume BIGINT,
    bid5_price DECIMAL(10,3),
    -- 卖五档
    ask1_volume BIGINT,
    ask1_price DECIMAL(10,3),
    ask2_volume BIGINT,
    ask2_price DECIMAL(10,3),
    ask3_volume BIGINT,
    ask3_price DECIMAL(10,3),
    ask4_volume BIGINT,
    ask4_price DECIMAL(10,3),
    ask5_volume BIGINT,
    ask5_price DECIMAL(10,3),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 逐笔成交表 (lv2)
CREATE TABLE IF NOT EXISTS lv2_tick_data (
    id BIGSERIAL PRIMARY KEY,
    symbol VARCHAR(20) NOT NULL,
    trade_time TIMESTAMP NOT NULL,
    trade_id VARCHAR(50) NOT NULL,
    trade_price DECIMAL(10,3) NOT NULL,
    trade_volume BIGINT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 十档盘口表 (lv10)
CREATE TABLE IF NOT EXISTS lv10_depth_data (
    id BIGSERIAL PRIMARY KEY,
    symbol VARCHAR(20) NOT NULL,
    symbol_name VARCHAR(100),
    push_time TIMESTAMP NOT NULL,
    latest_price DECIMAL(10,3),
    prev_close DECIMAL(10,3),
    turnover BIGINT,
    volume BIGINT,
    -- 买十档
    bid1_volume BIGINT, bid1_price DECIMAL(10,3),
    bid2_volume BIGINT, bid2_price DECIMAL(10,3),
    bid3_volume BIGINT, bid3_price DECIMAL(10,3),
    bid4_volume BIGINT, bid4_price DECIMAL(10,3),
    bid5_volume BIGINT, bid5_price DECIMAL(10,3),
    bid6_volume BIGINT, bid6_price DECIMAL(10,3),
    bid7_volume BIGINT, bid7_price DECIMAL(10,3),
    bid8_volume BIGINT, bid8_price DECIMAL(10,3),
    bid9_volume BIGINT, bid9_price DECIMAL(10,3),
    bid10_volume BIGINT, bid10_price DECIMAL(10,3),
    -- 卖十档
    ask1_volume BIGINT, ask1_price DECIMAL(10,3),
    ask2_volume BIGINT, ask2_price DECIMAL(10,3),
    ask3_volume BIGINT, ask3_price DECIMAL(10,3),
    ask4_volume BIGINT, ask4_price DECIMAL(10,3),
    ask5_volume BIGINT, ask5_price DECIMAL(10,3),
    ask6_volume BIGINT, ask6_price DECIMAL(10,3),
    ask7_volume BIGINT, ask7_price DECIMAL(10,3),
    ask8_volume BIGINT, ask8_price DECIMAL(10,3),
    ask9_volume BIGINT, ask9_price DECIMAL(10,3),
    ask10_volume BIGINT, ask10_price DECIMAL(10,3),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_lv1_symbol_time ON lv1_market_data(symbol, push_time);
CREATE INDEX IF NOT EXISTS idx_lv2_symbol_time ON lv2_tick_data(symbol, trade_time);
CREATE INDEX IF NOT EXISTS idx_lv10_symbol_time ON lv10_depth_data(symbol, push_time);

-- 创建分区表（可选，用于大数据量场景）
-- 按日期分区可以提高查询性能和数据管理效率
