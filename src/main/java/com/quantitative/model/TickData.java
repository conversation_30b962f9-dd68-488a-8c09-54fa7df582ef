package com.quantitative.model;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 逐笔成交数据模型 (lv2)
 */
public class TickData {
    private Long id;
    private String symbol;
    private LocalDateTime tradeTime;
    private String tradeId;
    private BigDecimal tradePrice;
    private Long tradeVolume;
    private LocalDateTime createdAt;

    // 构造函数
    public TickData() {}

    public TickData(String symbol, LocalDateTime tradeTime, String tradeId, 
                   BigDecimal tradePrice, Long tradeVolume) {
        this.symbol = symbol;
        this.tradeTime = tradeTime;
        this.tradeId = tradeId;
        this.tradePrice = tradePrice;
        this.tradeVolume = tradeVolume;
    }

    // Getter和Setter方法
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getSymbol() {
        return symbol;
    }

    public void setSymbol(String symbol) {
        this.symbol = symbol;
    }

    public LocalDateTime getTradeTime() {
        return tradeTime;
    }

    public void setTradeTime(LocalDateTime tradeTime) {
        this.tradeTime = tradeTime;
    }

    public String getTradeId() {
        return tradeId;
    }

    public void setTradeId(String tradeId) {
        this.tradeId = tradeId;
    }

    public BigDecimal getTradePrice() {
        return tradePrice;
    }

    public void setTradePrice(BigDecimal tradePrice) {
        this.tradePrice = tradePrice;
    }

    public Long getTradeVolume() {
        return tradeVolume;
    }

    public void setTradeVolume(Long tradeVolume) {
        this.tradeVolume = tradeVolume;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    @Override
    public String toString() {
        return "TickData{" +
                "symbol='" + symbol + '\'' +
                ", tradeTime=" + tradeTime +
                ", tradeId='" + tradeId + '\'' +
                ", tradePrice=" + tradePrice +
                ", tradeVolume=" + tradeVolume +
                '}';
    }
}
