package com.quantitative.model;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 十档盘口数据模型 (lv10)
 */
public class DepthData {
    private Long id;
    private String symbol;
    private String symbolName;
    private LocalDateTime pushTime;
    private BigDecimal latestPrice;
    private BigDecimal prevClose;
    private Long turnover;
    private Long volume;
    
    // 买十档
    private Long bid1Volume; private BigDecimal bid1Price;
    private Long bid2Volume; private BigDecimal bid2Price;
    private Long bid3Volume; private BigDecimal bid3Price;
    private Long bid4Volume; private BigDecimal bid4Price;
    private Long bid5Volume; private BigDecimal bid5Price;
    private Long bid6Volume; private BigDecimal bid6Price;
    private Long bid7Volume; private BigDecimal bid7Price;
    private Long bid8Volume; private BigDecimal bid8Price;
    private Long bid9Volume; private BigDecimal bid9Price;
    private Long bid10Volume; private BigDecimal bid10Price;
    
    // 卖十档
    private Long ask1Volume; private BigDecimal ask1Price;
    private Long ask2Volume; private BigDecimal ask2Price;
    private Long ask3Volume; private BigDecimal ask3Price;
    private Long ask4Volume; private BigDecimal ask4Price;
    private Long ask5Volume; private BigDecimal ask5Price;
    private Long ask6Volume; private BigDecimal ask6Price;
    private Long ask7Volume; private BigDecimal ask7Price;
    private Long ask8Volume; private BigDecimal ask8Price;
    private Long ask9Volume; private BigDecimal ask9Price;
    private Long ask10Volume; private BigDecimal ask10Price;
    
    private LocalDateTime createdAt;

    // 构造函数
    public DepthData() {}

    public DepthData(String symbol, String symbolName, LocalDateTime pushTime) {
        this.symbol = symbol;
        this.symbolName = symbolName;
        this.pushTime = pushTime;
    }

    // 基础字段的getter/setter
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }

    public String getSymbol() { return symbol; }
    public void setSymbol(String symbol) { this.symbol = symbol; }

    public String getSymbolName() { return symbolName; }
    public void setSymbolName(String symbolName) { this.symbolName = symbolName; }

    public LocalDateTime getPushTime() { return pushTime; }
    public void setPushTime(LocalDateTime pushTime) { this.pushTime = pushTime; }

    public BigDecimal getLatestPrice() { return latestPrice; }
    public void setLatestPrice(BigDecimal latestPrice) { this.latestPrice = latestPrice; }

    public BigDecimal getPrevClose() { return prevClose; }
    public void setPrevClose(BigDecimal prevClose) { this.prevClose = prevClose; }

    public Long getTurnover() { return turnover; }
    public void setTurnover(Long turnover) { this.turnover = turnover; }

    public Long getVolume() { return volume; }
    public void setVolume(Long volume) { this.volume = volume; }

    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }

    // 买档位的getter/setter方法
    public Long getBid1Volume() { return bid1Volume; }
    public void setBid1Volume(Long bid1Volume) { this.bid1Volume = bid1Volume; }
    public BigDecimal getBid1Price() { return bid1Price; }
    public void setBid1Price(BigDecimal bid1Price) { this.bid1Price = bid1Price; }

    public Long getBid2Volume() { return bid2Volume; }
    public void setBid2Volume(Long bid2Volume) { this.bid2Volume = bid2Volume; }
    public BigDecimal getBid2Price() { return bid2Price; }
    public void setBid2Price(BigDecimal bid2Price) { this.bid2Price = bid2Price; }

    public Long getBid3Volume() { return bid3Volume; }
    public void setBid3Volume(Long bid3Volume) { this.bid3Volume = bid3Volume; }
    public BigDecimal getBid3Price() { return bid3Price; }
    public void setBid3Price(BigDecimal bid3Price) { this.bid3Price = bid3Price; }

    public Long getBid4Volume() { return bid4Volume; }
    public void setBid4Volume(Long bid4Volume) { this.bid4Volume = bid4Volume; }
    public BigDecimal getBid4Price() { return bid4Price; }
    public void setBid4Price(BigDecimal bid4Price) { this.bid4Price = bid4Price; }

    public Long getBid5Volume() { return bid5Volume; }
    public void setBid5Volume(Long bid5Volume) { this.bid5Volume = bid5Volume; }
    public BigDecimal getBid5Price() { return bid5Price; }
    public void setBid5Price(BigDecimal bid5Price) { this.bid5Price = bid5Price; }

    public Long getBid6Volume() { return bid6Volume; }
    public void setBid6Volume(Long bid6Volume) { this.bid6Volume = bid6Volume; }
    public BigDecimal getBid6Price() { return bid6Price; }
    public void setBid6Price(BigDecimal bid6Price) { this.bid6Price = bid6Price; }

    public Long getBid7Volume() { return bid7Volume; }
    public void setBid7Volume(Long bid7Volume) { this.bid7Volume = bid7Volume; }
    public BigDecimal getBid7Price() { return bid7Price; }
    public void setBid7Price(BigDecimal bid7Price) { this.bid7Price = bid7Price; }

    public Long getBid8Volume() { return bid8Volume; }
    public void setBid8Volume(Long bid8Volume) { this.bid8Volume = bid8Volume; }
    public BigDecimal getBid8Price() { return bid8Price; }
    public void setBid8Price(BigDecimal bid8Price) { this.bid8Price = bid8Price; }

    public Long getBid9Volume() { return bid9Volume; }
    public void setBid9Volume(Long bid9Volume) { this.bid9Volume = bid9Volume; }
    public BigDecimal getBid9Price() { return bid9Price; }
    public void setBid9Price(BigDecimal bid9Price) { this.bid9Price = bid9Price; }

    public Long getBid10Volume() { return bid10Volume; }
    public void setBid10Volume(Long bid10Volume) { this.bid10Volume = bid10Volume; }
    public BigDecimal getBid10Price() { return bid10Price; }
    public void setBid10Price(BigDecimal bid10Price) { this.bid10Price = bid10Price; }

    // 卖档位的getter/setter方法
    public Long getAsk1Volume() { return ask1Volume; }
    public void setAsk1Volume(Long ask1Volume) { this.ask1Volume = ask1Volume; }
    public BigDecimal getAsk1Price() { return ask1Price; }
    public void setAsk1Price(BigDecimal ask1Price) { this.ask1Price = ask1Price; }

    public Long getAsk2Volume() { return ask2Volume; }
    public void setAsk2Volume(Long ask2Volume) { this.ask2Volume = ask2Volume; }
    public BigDecimal getAsk2Price() { return ask2Price; }
    public void setAsk2Price(BigDecimal ask2Price) { this.ask2Price = ask2Price; }

    public Long getAsk3Volume() { return ask3Volume; }
    public void setAsk3Volume(Long ask3Volume) { this.ask3Volume = ask3Volume; }
    public BigDecimal getAsk3Price() { return ask3Price; }
    public void setAsk3Price(BigDecimal ask3Price) { this.ask3Price = ask3Price; }

    public Long getAsk4Volume() { return ask4Volume; }
    public void setAsk4Volume(Long ask4Volume) { this.ask4Volume = ask4Volume; }
    public BigDecimal getAsk4Price() { return ask4Price; }
    public void setAsk4Price(BigDecimal ask4Price) { this.ask4Price = ask4Price; }

    public Long getAsk5Volume() { return ask5Volume; }
    public void setAsk5Volume(Long ask5Volume) { this.ask5Volume = ask5Volume; }
    public BigDecimal getAsk5Price() { return ask5Price; }
    public void setAsk5Price(BigDecimal ask5Price) { this.ask5Price = ask5Price; }

    public Long getAsk6Volume() { return ask6Volume; }
    public void setAsk6Volume(Long ask6Volume) { this.ask6Volume = ask6Volume; }
    public BigDecimal getAsk6Price() { return ask6Price; }
    public void setAsk6Price(BigDecimal ask6Price) { this.ask6Price = ask6Price; }

    public Long getAsk7Volume() { return ask7Volume; }
    public void setAsk7Volume(Long ask7Volume) { this.ask7Volume = ask7Volume; }
    public BigDecimal getAsk7Price() { return ask7Price; }
    public void setAsk7Price(BigDecimal ask7Price) { this.ask7Price = ask7Price; }

    public Long getAsk8Volume() { return ask8Volume; }
    public void setAsk8Volume(Long ask8Volume) { this.ask8Volume = ask8Volume; }
    public BigDecimal getAsk8Price() { return ask8Price; }
    public void setAsk8Price(BigDecimal ask8Price) { this.ask8Price = ask8Price; }

    public Long getAsk9Volume() { return ask9Volume; }
    public void setAsk9Volume(Long ask9Volume) { this.ask9Volume = ask9Volume; }
    public BigDecimal getAsk9Price() { return ask9Price; }
    public void setAsk9Price(BigDecimal ask9Price) { this.ask9Price = ask9Price; }

    public Long getAsk10Volume() { return ask10Volume; }
    public void setAsk10Volume(Long ask10Volume) { this.ask10Volume = ask10Volume; }
    public BigDecimal getAsk10Price() { return ask10Price; }
    public void setAsk10Price(BigDecimal ask10Price) { this.ask10Price = ask10Price; }

    @Override
    public String toString() {
        return "DepthData{" +
                "symbol='" + symbol + '\'' +
                ", symbolName='" + symbolName + '\'' +
                ", pushTime=" + pushTime +
                ", latestPrice=" + latestPrice +
                ", prevClose=" + prevClose +
                ", turnover=" + turnover +
                ", volume=" + volume +
                '}';
    }
}
