package com.quantitative.model;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 基础行情数据模型 (lv1)
 */
public class MarketData {
    private Long id;
    private String symbol;
    private String symbolName;
    private LocalDateTime pushTime;
    private BigDecimal latestPrice;
    private BigDecimal changePercent;
    private Long turnover;
    private Long volume;
    
    // 买五档
    private Long bid1Volume;
    private BigDecimal bid1Price;
    private Long bid2Volume;
    private BigDecimal bid2Price;
    private Long bid3Volume;
    private BigDecimal bid3Price;
    private Long bid4Volume;
    private BigDecimal bid4Price;
    private Long bid5Volume;
    private BigDecimal bid5Price;
    
    // 卖五档
    private Long ask1Volume;
    private BigDecimal ask1Price;
    private Long ask2Volume;
    private BigDecimal ask2Price;
    private Long ask3Volume;
    private BigDecimal ask3Price;
    private Long ask4Volume;
    private BigDecimal ask4Price;
    private Long ask5Volume;
    private BigDecimal ask5Price;
    
    private LocalDateTime createdAt;

    // 构造函数
    public MarketData() {}

    public MarketData(String symbol, String symbolName, LocalDateTime pushTime) {
        this.symbol = symbol;
        this.symbolName = symbolName;
        this.pushTime = pushTime;
    }

    // Getter和Setter方法
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }

    public String getSymbol() { return symbol; }
    public void setSymbol(String symbol) { this.symbol = symbol; }

    public String getSymbolName() { return symbolName; }
    public void setSymbolName(String symbolName) { this.symbolName = symbolName; }

    public LocalDateTime getPushTime() { return pushTime; }
    public void setPushTime(LocalDateTime pushTime) { this.pushTime = pushTime; }

    public BigDecimal getLatestPrice() { return latestPrice; }
    public void setLatestPrice(BigDecimal latestPrice) { this.latestPrice = latestPrice; }

    public BigDecimal getChangePercent() { return changePercent; }
    public void setChangePercent(BigDecimal changePercent) { this.changePercent = changePercent; }

    public Long getTurnover() { return turnover; }
    public void setTurnover(Long turnover) { this.turnover = turnover; }

    public Long getVolume() { return volume; }
    public void setVolume(Long volume) { this.volume = volume; }

    // 买档位的getter/setter
    public Long getBid1Volume() { return bid1Volume; }
    public void setBid1Volume(Long bid1Volume) { this.bid1Volume = bid1Volume; }

    public BigDecimal getBid1Price() { return bid1Price; }
    public void setBid1Price(BigDecimal bid1Price) { this.bid1Price = bid1Price; }

    public Long getBid2Volume() { return bid2Volume; }
    public void setBid2Volume(Long bid2Volume) { this.bid2Volume = bid2Volume; }

    public BigDecimal getBid2Price() { return bid2Price; }
    public void setBid2Price(BigDecimal bid2Price) { this.bid2Price = bid2Price; }

    public Long getBid3Volume() { return bid3Volume; }
    public void setBid3Volume(Long bid3Volume) { this.bid3Volume = bid3Volume; }

    public BigDecimal getBid3Price() { return bid3Price; }
    public void setBid3Price(BigDecimal bid3Price) { this.bid3Price = bid3Price; }

    public Long getBid4Volume() { return bid4Volume; }
    public void setBid4Volume(Long bid4Volume) { this.bid4Volume = bid4Volume; }

    public BigDecimal getBid4Price() { return bid4Price; }
    public void setBid4Price(BigDecimal bid4Price) { this.bid4Price = bid4Price; }

    public Long getBid5Volume() { return bid5Volume; }
    public void setBid5Volume(Long bid5Volume) { this.bid5Volume = bid5Volume; }

    public BigDecimal getBid5Price() { return bid5Price; }
    public void setBid5Price(BigDecimal bid5Price) { this.bid5Price = bid5Price; }

    // 卖档位的getter/setter
    public Long getAsk1Volume() { return ask1Volume; }
    public void setAsk1Volume(Long ask1Volume) { this.ask1Volume = ask1Volume; }

    public BigDecimal getAsk1Price() { return ask1Price; }
    public void setAsk1Price(BigDecimal ask1Price) { this.ask1Price = ask1Price; }

    public Long getAsk2Volume() { return ask2Volume; }
    public void setAsk2Volume(Long ask2Volume) { this.ask2Volume = ask2Volume; }

    public BigDecimal getAsk2Price() { return ask2Price; }
    public void setAsk2Price(BigDecimal ask2Price) { this.ask2Price = ask2Price; }

    public Long getAsk3Volume() { return ask3Volume; }
    public void setAsk3Volume(Long ask3Volume) { this.ask3Volume = ask3Volume; }

    public BigDecimal getAsk3Price() { return ask3Price; }
    public void setAsk3Price(BigDecimal ask3Price) { this.ask3Price = ask3Price; }

    public Long getAsk4Volume() { return ask4Volume; }
    public void setAsk4Volume(Long ask4Volume) { this.ask4Volume = ask4Volume; }

    public BigDecimal getAsk4Price() { return ask4Price; }
    public void setAsk4Price(BigDecimal ask4Price) { this.ask4Price = ask4Price; }

    public Long getAsk5Volume() { return ask5Volume; }
    public void setAsk5Volume(Long ask5Volume) { this.ask5Volume = ask5Volume; }

    public BigDecimal getAsk5Price() { return ask5Price; }
    public void setAsk5Price(BigDecimal ask5Price) { this.ask5Price = ask5Price; }

    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }

    @Override
    public String toString() {
        return "MarketData{" +
                "symbol='" + symbol + '\'' +
                ", symbolName='" + symbolName + '\'' +
                ", pushTime=" + pushTime +
                ", latestPrice=" + latestPrice +
                ", changePercent=" + changePercent +
                ", turnover=" + turnover +
                ", volume=" + volume +
                '}';
    }
}
