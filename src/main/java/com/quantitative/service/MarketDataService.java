package com.quantitative.service;

import com.quantitative.dao.MarketDataDao;
import com.quantitative.model.DepthData;
import com.quantitative.model.MarketData;
import com.quantitative.model.TickData;
import com.quantitative.parser.MarketDataParser;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 市场数据业务服务
 */
public class MarketDataService {
    private static final Logger logger = LoggerFactory.getLogger(MarketDataService.class);
    
    private final MarketDataParser parser;
    private final MarketDataDao dao;
    private final ExecutorService executorService;
    private final BlockingQueue<String> dataQueue;
    
    // 统计信息
    private final AtomicLong totalProcessed = new AtomicLong(0);
    private final AtomicLong lv1Count = new AtomicLong(0);
    private final AtomicLong lv2Count = new AtomicLong(0);
    private final AtomicLong lv10Count = new AtomicLong(0);
    private final AtomicLong errorCount = new AtomicLong(0);

    public MarketDataService() {
        this.parser = new MarketDataParser();
        this.dao = new MarketDataDao();
        this.executorService = Executors.newFixedThreadPool(4);
        this.dataQueue = new LinkedBlockingQueue<>(10000);
        
        // 启动数据处理线程
        startDataProcessingThreads();
        
        // 启动统计线程
        startStatisticsThread();
        
        logger.info("市场数据服务初始化完成");
    }

    /**
     * 处理接收到的市场数据
     */
    public void processMarketData(String data) {
        if (data == null || data.trim().isEmpty()) {
            return;
        }

        try {
            // 将数据放入队列异步处理
            if (!dataQueue.offer(data)) {
                logger.warn("数据队列已满，丢弃数据");
                errorCount.incrementAndGet();
            }
        } catch (Exception e) {
            logger.error("处理市场数据失败", e);
            errorCount.incrementAndGet();
        }
    }

    /**
     * 启动数据处理线程
     */
    private void startDataProcessingThreads() {
        for (int i = 0; i < 3; i++) {
            executorService.submit(() -> {
                while (!Thread.currentThread().isInterrupted()) {
                    try {
                        String data = dataQueue.take();
                        processDataInternal(data);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        break;
                    } catch (Exception e) {
                        logger.error("数据处理线程异常", e);
                        errorCount.incrementAndGet();
                    }
                }
            });
        }
        logger.info("启动了 3 个数据处理线程");
    }

    /**
     * 内部数据处理方法
     */
    private void processDataInternal(String data) {
        try {
            List<Object> parsedData = parser.parseMarketData(data);
            
            if (parsedData.isEmpty()) {
                return;
            }

            // 分类处理不同类型的数据
            List<TickData> tickDataList = new ArrayList<>();
            
            for (Object item : parsedData) {
                if (item instanceof MarketData) {
                    dao.saveMarketData((MarketData) item);
                    lv1Count.incrementAndGet();
                } else if (item instanceof TickData) {
                    tickDataList.add((TickData) item);
                } else if (item instanceof DepthData) {
                    dao.saveDepthData((DepthData) item);
                    lv10Count.incrementAndGet();
                }
            }

            // 批量保存逐笔成交数据
            if (!tickDataList.isEmpty()) {
                dao.saveTickDataBatch(tickDataList);
                lv2Count.addAndGet(tickDataList.size());
            }

            totalProcessed.addAndGet(parsedData.size());
            
        } catch (Exception e) {
            logger.error("处理数据失败: {}", data.substring(0, Math.min(100, data.length())), e);
            errorCount.incrementAndGet();
        }
    }

    /**
     * 启动统计线程
     */
    private void startStatisticsThread() {
        executorService.submit(() -> {
            while (!Thread.currentThread().isInterrupted()) {
                try {
                    Thread.sleep(60000); // 每分钟输出一次统计
                    printStatistics();
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                } catch (Exception e) {
                    logger.error("统计线程异常", e);
                }
            }
        });
    }

    /**
     * 打印统计信息
     */
    private void printStatistics() {
        logger.info("=== 市场数据处理统计 ===");
        logger.info("总处理数量: {}", totalProcessed.get());
        logger.info("基础行情(lv1): {}", lv1Count.get());
        logger.info("逐笔成交(lv2): {}", lv2Count.get());
        logger.info("十档盘口(lv10): {}", lv10Count.get());
        logger.info("错误数量: {}", errorCount.get());
        logger.info("队列大小: {}", dataQueue.size());
        logger.info("========================");
    }

    /**
     * 获取统计信息
     */
    public StatisticsInfo getStatistics() {
        return new StatisticsInfo(
            totalProcessed.get(),
            lv1Count.get(),
            lv2Count.get(),
            lv10Count.get(),
            errorCount.get(),
            dataQueue.size()
        );
    }

    /**
     * 关闭服务
     */
    public void shutdown() {
        logger.info("正在关闭市场数据服务...");
        
        executorService.shutdown();
        try {
            if (!executorService.awaitTermination(30, java.util.concurrent.TimeUnit.SECONDS)) {
                executorService.shutdownNow();
            }
        } catch (InterruptedException e) {
            executorService.shutdownNow();
        }
        
        // 处理剩余队列中的数据
        logger.info("处理剩余队列数据，数量: {}", dataQueue.size());
        while (!dataQueue.isEmpty()) {
            try {
                String data = dataQueue.poll();
                if (data != null) {
                    processDataInternal(data);
                }
            } catch (Exception e) {
                logger.error("处理剩余数据失败", e);
            }
        }
        
        printStatistics();
        logger.info("市场数据服务已关闭");
    }

    /**
     * 统计信息类
     */
    public static class StatisticsInfo {
        private final long totalProcessed;
        private final long lv1Count;
        private final long lv2Count;
        private final long lv10Count;
        private final long errorCount;
        private final int queueSize;

        public StatisticsInfo(long totalProcessed, long lv1Count, long lv2Count, 
                            long lv10Count, long errorCount, int queueSize) {
            this.totalProcessed = totalProcessed;
            this.lv1Count = lv1Count;
            this.lv2Count = lv2Count;
            this.lv10Count = lv10Count;
            this.errorCount = errorCount;
            this.queueSize = queueSize;
        }

        // Getter方法
        public long getTotalProcessed() { return totalProcessed; }
        public long getLv1Count() { return lv1Count; }
        public long getLv2Count() { return lv2Count; }
        public long getLv10Count() { return lv10Count; }
        public long getErrorCount() { return errorCount; }
        public int getQueueSize() { return queueSize; }

        @Override
        public String toString() {
            return String.format("StatisticsInfo{total=%d, lv1=%d, lv2=%d, lv10=%d, errors=%d, queue=%d}",
                totalProcessed, lv1Count, lv2Count, lv10Count, errorCount, queueSize);
        }
    }
}
