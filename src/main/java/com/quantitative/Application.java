package com.quantitative;

import com.quantitative.client.JvQuantWebSocketClient;
import com.quantitative.config.DatabaseConfig;
import com.quantitative.config.MarketConfig;
import com.quantitative.service.MarketDataService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Scanner;
import java.util.concurrent.CountDownLatch;

/**
 * 沪深股市数据收集主应用程序
 */
public class Application {
    private static final Logger logger = LoggerFactory.getLogger(Application.class);
    
    private static JvQuantWebSocketClient webSocketClient;
    private static MarketDataService marketDataService;
    private static DatabaseConfig databaseConfig;

    public static void main(String[] args) {
        logger.info("=== 沪深股市数据收集系统启动 ===");
        
        // 添加关闭钩子
        Runtime.getRuntime().addShutdownHook(new Thread(Application::shutdown));
        
        try {
            // 初始化系统
            initializeSystem();
            
            // 启动WebSocket连接
            startWebSocketConnection();
            
            // 启动控制台交互
            startConsoleInterface();
            
        } catch (Exception e) {
            logger.error("系统启动失败", e);
            System.exit(1);
        }
    }

    /**
     * 初始化系统
     */
    private static void initializeSystem() {
        logger.info("正在初始化系统...");
        
        try {
            // 初始化配置
            MarketConfig marketConfig = MarketConfig.getInstance();
            databaseConfig = DatabaseConfig.getInstance();
            
            // 初始化数据库
            logger.info("正在初始化数据库...");
            databaseConfig.initDatabase();
            
            // 初始化市场数据服务
            logger.info("正在初始化市场数据服务...");
            marketDataService = new MarketDataService();
            
            logger.info("系统初始化完成");
            
        } catch (Exception e) {
            logger.error("系统初始化失败", e);
            throw new RuntimeException("系统初始化失败", e);
        }
    }

    /**
     * 启动WebSocket连接
     */
    private static void startWebSocketConnection() {
        try {
            MarketConfig config = MarketConfig.getInstance();
            String wsUrl = config.getWebSocketUrl();
            
            if (wsUrl == null || wsUrl.trim().isEmpty()) {
                throw new RuntimeException("WebSocket URL未配置，请检查 application.properties 文件");
            }
            
            logger.info("正在连接到 jvQuant 服务器: {}", wsUrl);
            
            webSocketClient = new JvQuantWebSocketClient(wsUrl, marketDataService);
            webSocketClient.connect();
            
            // 等待连接建立
            Thread.sleep(3000);
            
            if (webSocketClient.isConnected()) {
                logger.info("WebSocket连接建立成功");
            } else {
                logger.warn("WebSocket连接可能未建立，请检查网络和配置");
            }
            
        } catch (Exception e) {
            logger.error("启动WebSocket连接失败", e);
            throw new RuntimeException("启动WebSocket连接失败", e);
        }
    }

    /**
     * 启动控制台交互界面
     */
    private static void startConsoleInterface() {
        logger.info("=== 控制台交互界面启动 ===");
        printHelp();
        
        Scanner scanner = new Scanner(System.in);
        CountDownLatch latch = new CountDownLatch(1);
        
        // 启动输入处理线程
        Thread inputThread = new Thread(() -> {
            while (!Thread.currentThread().isInterrupted()) {
                try {
                    System.out.print("> ");
                    String input = scanner.nextLine().trim();
                    
                    if (handleCommand(input)) {
                        break; // 退出命令
                    }
                } catch (Exception e) {
                    logger.error("处理命令失败", e);
                }
            }
            latch.countDown();
        });
        
        inputThread.setDaemon(false);
        inputThread.start();
        
        try {
            latch.await();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    /**
     * 处理控制台命令
     */
    private static boolean handleCommand(String command) {
        switch (command.toLowerCase()) {
            case "help":
            case "h":
                printHelp();
                break;
                
            case "status":
            case "s":
                printStatus();
                break;
                
            case "stats":
                printStatistics();
                break;
                
            case "reconnect":
            case "r":
                reconnectWebSocket();
                break;
                
            case "quit":
            case "exit":
            case "q":
                logger.info("正在退出系统...");
                return true;
                
            default:
                if (command.startsWith("sub ")) {
                    // 自定义订阅命令
                    String subCommand = command.substring(4);
                    if (webSocketClient != null && webSocketClient.isConnected()) {
                        webSocketClient.subscribe(subCommand);
                    } else {
                        System.out.println("WebSocket未连接");
                    }
                } else {
                    System.out.println("未知命令: " + command + "，输入 'help' 查看帮助");
                }
                break;
        }
        return false;
    }

    /**
     * 打印帮助信息
     */
    private static void printHelp() {
        System.out.println("\n=== 可用命令 ===");
        System.out.println("help (h)     - 显示帮助信息");
        System.out.println("status (s)   - 显示系统状态");
        System.out.println("stats        - 显示处理统计");
        System.out.println("reconnect (r)- 重新连接WebSocket");
        System.out.println("sub <cmd>    - 发送自定义订阅命令");
        System.out.println("quit (q)     - 退出系统");
        System.out.println("================\n");
    }

    /**
     * 打印系统状态
     */
    private static void printStatus() {
        System.out.println("\n=== 系统状态 ===");
        System.out.println("WebSocket连接: " + (webSocketClient != null && webSocketClient.isConnected() ? "已连接" : "未连接"));
        System.out.println("数据库连接: " + (databaseConfig != null ? "正常" : "异常"));
        System.out.println("市场数据服务: " + (marketDataService != null ? "运行中" : "未启动"));
        System.out.println("================\n");
    }

    /**
     * 打印处理统计
     */
    private static void printStatistics() {
        if (marketDataService != null) {
            MarketDataService.StatisticsInfo stats = marketDataService.getStatistics();
            System.out.println("\n=== 处理统计 ===");
            System.out.println("总处理数量: " + stats.getTotalProcessed());
            System.out.println("基础行情(lv1): " + stats.getLv1Count());
            System.out.println("逐笔成交(lv2): " + stats.getLv2Count());
            System.out.println("十档盘口(lv10): " + stats.getLv10Count());
            System.out.println("错误数量: " + stats.getErrorCount());
            System.out.println("队列大小: " + stats.getQueueSize());
            System.out.println("================\n");
        } else {
            System.out.println("市场数据服务未启动");
        }
    }

    /**
     * 重新连接WebSocket
     */
    private static void reconnectWebSocket() {
        try {
            if (webSocketClient != null) {
                webSocketClient.closeManually();
            }
            
            Thread.sleep(2000);
            startWebSocketConnection();
            System.out.println("重新连接完成");
            
        } catch (Exception e) {
            logger.error("重新连接失败", e);
            System.out.println("重新连接失败: " + e.getMessage());
        }
    }

    /**
     * 系统关闭处理
     */
    private static void shutdown() {
        logger.info("正在关闭系统...");
        
        try {
            if (webSocketClient != null) {
                webSocketClient.closeManually();
            }
            
            if (marketDataService != null) {
                marketDataService.shutdown();
            }
            
            if (databaseConfig != null) {
                databaseConfig.close();
            }
            
            logger.info("系统关闭完成");
            
        } catch (Exception e) {
            logger.error("系统关闭异常", e);
        }
    }
}
