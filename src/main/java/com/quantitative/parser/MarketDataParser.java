package com.quantitative.parser;

import com.quantitative.model.DepthData;
import com.quantitative.model.MarketData;
import com.quantitative.model.TickData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * 市场数据解析器
 */
public class MarketDataParser {
    private static final Logger logger = LoggerFactory.getLogger(MarketDataParser.class);
    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");

    /**
     * 解析压缩后的市场数据
     */
    public List<Object> parseMarketData(String data) {
        List<Object> results = new ArrayList<>();
        
        if (data == null || data.trim().isEmpty()) {
            return results;
        }

        String[] lines = data.split("\n");
        for (String line : lines) {
            line = line.trim();
            if (line.isEmpty()) continue;

            try {
                if (line.startsWith("lv1_")) {
                    MarketData marketData = parseLv1Data(line);
                    if (marketData != null) {
                        results.add(marketData);
                    }
                } else if (line.startsWith("lv2_")) {
                    List<TickData> tickDataList = parseLv2Data(line);
                    results.addAll(tickDataList);
                } else if (line.startsWith("lv10_")) {
                    DepthData depthData = parseLv10Data(line);
                    if (depthData != null) {
                        results.add(depthData);
                    }
                } else {
                    logger.debug("未知数据格式: {}", line);
                }
            } catch (Exception e) {
                logger.error("解析数据失败: {}", line, e);
            }
        }

        return results;
    }

    /**
     * 解析基础行情数据 (lv1)
     * 格式: lv1_证券代码=推送时间,证券名称,最新价格,涨幅,成交额,成交量,买五档[挂单数量,挂单价格],卖五档[挂单数量,挂单价格]
     */
    private MarketData parseLv1Data(String line) {
        try {
            String[] parts = line.split("=", 2);
            if (parts.length != 2) return null;

            String symbol = parts[0].substring(4); // 去掉 "lv1_" 前缀
            String[] fields = parts[1].split(",");

            if (fields.length < 6) {
                logger.warn("lv1数据字段不足: {}", line);
                return null;
            }

            MarketData data = new MarketData();
            data.setSymbol(symbol);
            data.setPushTime(parseDateTime(fields[0]));
            data.setSymbolName(fields[1]);
            data.setLatestPrice(parseBigDecimal(fields[2]));
            data.setChangePercent(parseBigDecimal(fields[3]));
            data.setTurnover(parseLong(fields[4]));
            data.setVolume(parseLong(fields[5]));

            // 解析买卖五档数据
            int index = 6;
            if (fields.length >= index + 10) {
                // 买五档
                data.setBid1Volume(parseLong(fields[index++]));
                data.setBid1Price(parseBigDecimal(fields[index++]));
                data.setBid2Volume(parseLong(fields[index++]));
                data.setBid2Price(parseBigDecimal(fields[index++]));
                data.setBid3Volume(parseLong(fields[index++]));
                data.setBid3Price(parseBigDecimal(fields[index++]));
                data.setBid4Volume(parseLong(fields[index++]));
                data.setBid4Price(parseBigDecimal(fields[index++]));
                data.setBid5Volume(parseLong(fields[index++]));
                data.setBid5Price(parseBigDecimal(fields[index++]));
            }

            if (fields.length >= index + 10) {
                // 卖五档
                data.setAsk1Volume(parseLong(fields[index++]));
                data.setAsk1Price(parseBigDecimal(fields[index++]));
                data.setAsk2Volume(parseLong(fields[index++]));
                data.setAsk2Price(parseBigDecimal(fields[index++]));
                data.setAsk3Volume(parseLong(fields[index++]));
                data.setAsk3Price(parseBigDecimal(fields[index++]));
                data.setAsk4Volume(parseLong(fields[index++]));
                data.setAsk4Price(parseBigDecimal(fields[index++]));
                data.setAsk5Volume(parseLong(fields[index++]));
                data.setAsk5Price(parseBigDecimal(fields[index++]));
            }

            return data;
        } catch (Exception e) {
            logger.error("解析lv1数据失败: {}", line, e);
            return null;
        }
    }

    /**
     * 解析逐笔成交数据 (lv2)
     * 格式: lv2_证券代码=成交时间1,成交编号1,成交价格1,成交数量1|成交时间2,成交编号2,成交价格2,成交数量2...
     */
    private List<TickData> parseLv2Data(String line) {
        List<TickData> results = new ArrayList<>();
        
        try {
            String[] parts = line.split("=", 2);
            if (parts.length != 2) return results;

            String symbol = parts[0].substring(4); // 去掉 "lv2_" 前缀
            String[] trades = parts[1].split("\\|");

            for (String trade : trades) {
                String[] fields = trade.split(",");
                if (fields.length >= 4) {
                    TickData tickData = new TickData();
                    tickData.setSymbol(symbol);
                    tickData.setTradeTime(parseDateTime(fields[0]));
                    tickData.setTradeId(fields[1]);
                    tickData.setTradePrice(parseBigDecimal(fields[2]));
                    tickData.setTradeVolume(parseLong(fields[3]));
                    results.add(tickData);
                }
            }
        } catch (Exception e) {
            logger.error("解析lv2数据失败: {}", line, e);
        }

        return results;
    }

    /**
     * 解析十档盘口数据 (lv10)
     * 格式: lv10_证券代码=推送时间,证券名称,最新价格,昨收,成交额,成交量,买十档[挂单数量,挂单价格],卖十档[挂单数量,挂单价格]
     */
    private DepthData parseLv10Data(String line) {
        try {
            String[] parts = line.split("=", 2);
            if (parts.length != 2) return null;

            String symbol = parts[0].substring(5); // 去掉 "lv10_" 前缀
            String[] fields = parts[1].split(",");

            if (fields.length < 6) {
                logger.warn("lv10数据字段不足: {}", line);
                return null;
            }

            DepthData data = new DepthData();
            data.setSymbol(symbol);
            data.setPushTime(parseDateTime(fields[0]));
            data.setSymbolName(fields[1]);
            data.setLatestPrice(parseBigDecimal(fields[2]));
            data.setPrevClose(parseBigDecimal(fields[3]));
            data.setTurnover(parseLong(fields[4]));
            data.setVolume(parseLong(fields[5]));

            // 解析买卖十档数据
            int index = 6;
            if (fields.length >= index + 40) { // 买十档 + 卖十档 = 40个字段
                // 买十档
                data.setBid1Volume(parseLong(fields[index++])); data.setBid1Price(parseBigDecimal(fields[index++]));
                data.setBid2Volume(parseLong(fields[index++])); data.setBid2Price(parseBigDecimal(fields[index++]));
                data.setBid3Volume(parseLong(fields[index++])); data.setBid3Price(parseBigDecimal(fields[index++]));
                data.setBid4Volume(parseLong(fields[index++])); data.setBid4Price(parseBigDecimal(fields[index++]));
                data.setBid5Volume(parseLong(fields[index++])); data.setBid5Price(parseBigDecimal(fields[index++]));
                data.setBid6Volume(parseLong(fields[index++])); data.setBid6Price(parseBigDecimal(fields[index++]));
                data.setBid7Volume(parseLong(fields[index++])); data.setBid7Price(parseBigDecimal(fields[index++]));
                data.setBid8Volume(parseLong(fields[index++])); data.setBid8Price(parseBigDecimal(fields[index++]));
                data.setBid9Volume(parseLong(fields[index++])); data.setBid9Price(parseBigDecimal(fields[index++]));
                data.setBid10Volume(parseLong(fields[index++])); data.setBid10Price(parseBigDecimal(fields[index++]));

                // 卖十档
                data.setAsk1Volume(parseLong(fields[index++])); data.setAsk1Price(parseBigDecimal(fields[index++]));
                data.setAsk2Volume(parseLong(fields[index++])); data.setAsk2Price(parseBigDecimal(fields[index++]));
                data.setAsk3Volume(parseLong(fields[index++])); data.setAsk3Price(parseBigDecimal(fields[index++]));
                data.setAsk4Volume(parseLong(fields[index++])); data.setAsk4Price(parseBigDecimal(fields[index++]));
                data.setAsk5Volume(parseLong(fields[index++])); data.setAsk5Price(parseBigDecimal(fields[index++]));
                data.setAsk6Volume(parseLong(fields[index++])); data.setAsk6Price(parseBigDecimal(fields[index++]));
                data.setAsk7Volume(parseLong(fields[index++])); data.setAsk7Price(parseBigDecimal(fields[index++]));
                data.setAsk8Volume(parseLong(fields[index++])); data.setAsk8Price(parseBigDecimal(fields[index++]));
                data.setAsk9Volume(parseLong(fields[index++])); data.setAsk9Price(parseBigDecimal(fields[index++]));
                data.setAsk10Volume(parseLong(fields[index++])); data.setAsk10Price(parseBigDecimal(fields[index++]));
            }

            return data;
        } catch (Exception e) {
            logger.error("解析lv10数据失败: {}", line, e);
            return null;
        }
    }

    private LocalDateTime parseDateTime(String timeStr) {
        try {
            if (timeStr == null || timeStr.trim().isEmpty()) {
                return LocalDateTime.now();
            }
            // 根据实际时间格式调整
            return LocalDateTime.parse(timeStr.trim(), TIME_FORMATTER);
        } catch (Exception e) {
            logger.warn("时间解析失败，使用当前时间: {}", timeStr);
            return LocalDateTime.now();
        }
    }

    private BigDecimal parseBigDecimal(String value) {
        try {
            if (value == null || value.trim().isEmpty()) {
                return null;
            }
            return new BigDecimal(value.trim());
        } catch (Exception e) {
            logger.warn("数值解析失败: {}", value);
            return null;
        }
    }

    private Long parseLong(String value) {
        try {
            if (value == null || value.trim().isEmpty()) {
                return null;
            }
            return Long.parseLong(value.trim());
        } catch (Exception e) {
            logger.warn("长整型解析失败: {}", value);
            return null;
        }
    }
}
