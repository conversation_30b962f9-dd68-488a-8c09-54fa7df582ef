package com.quantitative.config;

import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.sql.DataSource;
import java.io.IOException;
import java.io.InputStream;
import java.sql.Connection;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.Properties;

/**
 * 数据库配置类
 */
public class DatabaseConfig {
    private static final Logger logger = LoggerFactory.getLogger(DatabaseConfig.class);
    private static DatabaseConfig instance;
    private HikariDataSource dataSource;
    private Properties properties;

    private DatabaseConfig() {
        loadProperties();
        initDataSource();
    }

    public static synchronized DatabaseConfig getInstance() {
        if (instance == null) {
            instance = new DatabaseConfig();
        }
        return instance;
    }

    private void loadProperties() {
        properties = new Properties();
        try (InputStream input = getClass().getClassLoader().getResourceAsStream("application.properties")) {
            if (input == null) {
                throw new RuntimeException("无法找到 application.properties 文件");
            }
            properties.load(input);
        } catch (IOException e) {
            throw new RuntimeException("加载配置文件失败", e);
        }
    }

    private void initDataSource() {
        HikariConfig config = new HikariConfig();
        
        // 基础连接配置
        config.setJdbcUrl(properties.getProperty("database.url"));
        config.setUsername(properties.getProperty("database.username"));
        config.setPassword(properties.getProperty("database.password"));
        config.setDriverClassName(properties.getProperty("database.driver"));
        
        // 连接池配置
        config.setMaximumPoolSize(Integer.parseInt(
            properties.getProperty("database.pool.maximum.size", "20")));
        config.setMinimumIdle(Integer.parseInt(
            properties.getProperty("database.pool.minimum.idle", "5")));
        config.setConnectionTimeout(Long.parseLong(
            properties.getProperty("database.pool.connection.timeout", "30000")));
        config.setIdleTimeout(Long.parseLong(
            properties.getProperty("database.pool.idle.timeout", "600000")));
        config.setMaxLifetime(Long.parseLong(
            properties.getProperty("database.pool.max.lifetime", "1800000")));
        
        // 连接池名称
        config.setPoolName("MarketDataPool");
        
        // 连接测试
        config.setConnectionTestQuery("SELECT 1");
        
        dataSource = new HikariDataSource(config);
        logger.info("数据库连接池初始化完成");
    }

    public DataSource getDataSource() {
        return dataSource;
    }

    public Connection getConnection() throws SQLException {
        return dataSource.getConnection();
    }

    /**
     * 初始化数据库表结构
     */
    public void initDatabase() {
        try (InputStream input = getClass().getClassLoader().getResourceAsStream("schema.sql")) {
            if (input == null) {
                logger.warn("未找到 schema.sql 文件，跳过数据库初始化");
                return;
            }
            
            String sql = new String(input.readAllBytes());
            String[] statements = sql.split(";");
            
            try (Connection conn = getConnection();
                 Statement stmt = conn.createStatement()) {
                
                for (String statement : statements) {
                    String trimmed = statement.trim();
                    if (!trimmed.isEmpty() && !trimmed.startsWith("--")) {
                        stmt.execute(trimmed);
                    }
                }
                logger.info("数据库表结构初始化完成");
            }
        } catch (Exception e) {
            logger.error("数据库初始化失败", e);
            throw new RuntimeException("数据库初始化失败", e);
        }
    }

    /**
     * 关闭数据源
     */
    public void close() {
        if (dataSource != null && !dataSource.isClosed()) {
            dataSource.close();
            logger.info("数据库连接池已关闭");
        }
    }
}
