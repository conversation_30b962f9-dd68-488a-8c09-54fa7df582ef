package com.quantitative.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.InputStream;
import java.util.Arrays;
import java.util.List;
import java.util.Properties;

/**
 * 市场数据配置类
 */
public class MarketConfig {
    private static final Logger logger = LoggerFactory.getLogger(MarketConfig.class);
    private static MarketConfig instance;
    private Properties properties;

    private MarketConfig() {
        loadProperties();
    }

    public static synchronized MarketConfig getInstance() {
        if (instance == null) {
            instance = new MarketConfig();
        }
        return instance;
    }

    private void loadProperties() {
        properties = new Properties();
        try (InputStream input = getClass().getClassLoader().getResourceAsStream("application.properties")) {
            if (input == null) {
                throw new RuntimeException("无法找到 application.properties 文件");
            }
            properties.load(input);
            logger.info("市场配置加载完成");
        } catch (IOException e) {
            throw new RuntimeException("加载配置文件失败", e);
        }
    }

    /**
     * 获取WebSocket连接URL
     */
    public String getWebSocketUrl() {
        return properties.getProperty("jvquant.websocket.url");
    }

    /**
     * 获取重连间隔（毫秒）
     */
    public long getReconnectInterval() {
        return Long.parseLong(properties.getProperty("jvquant.websocket.reconnect.interval", "5000"));
    }

    /**
     * 获取最大重连次数
     */
    public int getMaxReconnectAttempts() {
        return Integer.parseInt(properties.getProperty("jvquant.websocket.max.reconnect.attempts", "10"));
    }

    /**
     * 获取订阅的股票代码列表
     */
    public List<String> getSubscriptionSymbols() {
        String symbols = properties.getProperty("jvquant.subscription.symbols", "");
        return Arrays.asList(symbols.split(","));
    }

    /**
     * 获取订阅的数据类型列表
     */
    public List<String> getSubscriptionTypes() {
        String types = properties.getProperty("jvquant.subscription.types", "lv1");
        return Arrays.asList(types.split(","));
    }

    /**
     * 生成订阅命令
     */
    public String generateSubscriptionCommand() {
        List<String> symbols = getSubscriptionSymbols();
        List<String> types = getSubscriptionTypes();
        
        StringBuilder command = new StringBuilder("add=");
        boolean first = true;
        
        for (String symbol : symbols) {
            symbol = symbol.trim();
            if (symbol.isEmpty()) continue;
            
            for (String type : types) {
                type = type.trim();
                if (type.isEmpty()) continue;
                
                if (!first) {
                    command.append(",");
                }
                command.append(type).append("_").append(symbol);
                first = false;
            }
        }
        
        String result = command.toString();
        logger.info("生成订阅命令: {}", result);
        return result;
    }

    /**
     * 获取属性值
     */
    public String getProperty(String key) {
        return properties.getProperty(key);
    }

    /**
     * 获取属性值，带默认值
     */
    public String getProperty(String key, String defaultValue) {
        return properties.getProperty(key, defaultValue);
    }
}
