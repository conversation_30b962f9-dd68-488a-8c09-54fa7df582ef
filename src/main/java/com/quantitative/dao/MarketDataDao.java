package com.quantitative.dao;

import com.quantitative.config.DatabaseConfig;
import com.quantitative.model.DepthData;
import com.quantitative.model.MarketData;
import com.quantitative.model.TickData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.*;
import java.util.List;

/**
 * 市场数据数据访问对象
 */
public class MarketDataDao {
    private static final Logger logger = LoggerFactory.getLogger(MarketDataDao.class);
    private final DatabaseConfig databaseConfig;

    public MarketDataDao() {
        this.databaseConfig = DatabaseConfig.getInstance();
    }

    /**
     * 保存基础行情数据
     */
    public void saveMarketData(MarketData data) {
        String sql = """
            INSERT INTO lv1_market_data (
                symbol, symbol_name, push_time, latest_price, change_percent, turnover, volume,
                bid1_volume, bid1_price, bid2_volume, bid2_price, bid3_volume, bid3_price,
                bid4_volume, bid4_price, bid5_volume, bid5_price,
                ask1_volume, ask1_price, ask2_volume, ask2_price, ask3_volume, ask3_price,
                ask4_volume, ask4_price, ask5_volume, ask5_price
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """;

        try (Connection conn = databaseConfig.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {

            stmt.setString(1, data.getSymbol());
            stmt.setString(2, data.getSymbolName());
            stmt.setTimestamp(3, data.getPushTime() != null ? Timestamp.valueOf(data.getPushTime()) : null);
            stmt.setBigDecimal(4, data.getLatestPrice());
            stmt.setBigDecimal(5, data.getChangePercent());
            setLongOrNull(stmt, 6, data.getTurnover());
            setLongOrNull(stmt, 7, data.getVolume());

            // 买五档
            setLongOrNull(stmt, 8, data.getBid1Volume());
            stmt.setBigDecimal(9, data.getBid1Price());
            setLongOrNull(stmt, 10, data.getBid2Volume());
            stmt.setBigDecimal(11, data.getBid2Price());
            setLongOrNull(stmt, 12, data.getBid3Volume());
            stmt.setBigDecimal(13, data.getBid3Price());
            setLongOrNull(stmt, 14, data.getBid4Volume());
            stmt.setBigDecimal(15, data.getBid4Price());
            setLongOrNull(stmt, 16, data.getBid5Volume());
            stmt.setBigDecimal(17, data.getBid5Price());

            // 卖五档
            setLongOrNull(stmt, 18, data.getAsk1Volume());
            stmt.setBigDecimal(19, data.getAsk1Price());
            setLongOrNull(stmt, 20, data.getAsk2Volume());
            stmt.setBigDecimal(21, data.getAsk2Price());
            setLongOrNull(stmt, 22, data.getAsk3Volume());
            stmt.setBigDecimal(23, data.getAsk3Price());
            setLongOrNull(stmt, 24, data.getAsk4Volume());
            stmt.setBigDecimal(25, data.getAsk4Price());
            setLongOrNull(stmt, 26, data.getAsk5Volume());
            stmt.setBigDecimal(27, data.getAsk5Price());

            stmt.executeUpdate();
            logger.debug("保存基础行情数据: {}", data.getSymbol());

        } catch (SQLException e) {
            logger.error("保存基础行情数据失败: {}", data.getSymbol(), e);
            throw new RuntimeException("保存基础行情数据失败", e);
        }
    }

    /**
     * 批量保存逐笔成交数据
     */
    public void saveTickDataBatch(List<TickData> dataList) {
        if (dataList == null || dataList.isEmpty()) {
            return;
        }

        String sql = """
            INSERT INTO lv2_tick_data (symbol, trade_time, trade_id, trade_price, trade_volume)
            VALUES (?, ?, ?, ?, ?)
            """;

        try (Connection conn = databaseConfig.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {

            conn.setAutoCommit(false);

            for (TickData data : dataList) {
                stmt.setString(1, data.getSymbol());
                stmt.setTimestamp(2, data.getTradeTime() != null ? Timestamp.valueOf(data.getTradeTime()) : null);
                stmt.setString(3, data.getTradeId());
                stmt.setBigDecimal(4, data.getTradePrice());
                setLongOrNull(stmt, 5, data.getTradeVolume());
                stmt.addBatch();
            }

            stmt.executeBatch();
            conn.commit();
            logger.debug("批量保存逐笔成交数据: {} 条", dataList.size());

        } catch (SQLException e) {
            logger.error("批量保存逐笔成交数据失败", e);
            throw new RuntimeException("批量保存逐笔成交数据失败", e);
        }
    }

    /**
     * 保存十档盘口数据
     */
    public void saveDepthData(DepthData data) {
        String sql = """
            INSERT INTO lv10_depth_data (
                symbol, symbol_name, push_time, latest_price, prev_close, turnover, volume,
                bid1_volume, bid1_price, bid2_volume, bid2_price, bid3_volume, bid3_price,
                bid4_volume, bid4_price, bid5_volume, bid5_price, bid6_volume, bid6_price,
                bid7_volume, bid7_price, bid8_volume, bid8_price, bid9_volume, bid9_price,
                bid10_volume, bid10_price,
                ask1_volume, ask1_price, ask2_volume, ask2_price, ask3_volume, ask3_price,
                ask4_volume, ask4_price, ask5_volume, ask5_price, ask6_volume, ask6_price,
                ask7_volume, ask7_price, ask8_volume, ask8_price, ask9_volume, ask9_price,
                ask10_volume, ask10_price
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """;

        try (Connection conn = databaseConfig.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {

            stmt.setString(1, data.getSymbol());
            stmt.setString(2, data.getSymbolName());
            stmt.setTimestamp(3, data.getPushTime() != null ? Timestamp.valueOf(data.getPushTime()) : null);
            stmt.setBigDecimal(4, data.getLatestPrice());
            stmt.setBigDecimal(5, data.getPrevClose());
            setLongOrNull(stmt, 6, data.getTurnover());
            setLongOrNull(stmt, 7, data.getVolume());

            // 买十档
            int index = 8;
            setLongOrNull(stmt, index++, data.getBid1Volume()); stmt.setBigDecimal(index++, data.getBid1Price());
            setLongOrNull(stmt, index++, data.getBid2Volume()); stmt.setBigDecimal(index++, data.getBid2Price());
            setLongOrNull(stmt, index++, data.getBid3Volume()); stmt.setBigDecimal(index++, data.getBid3Price());
            setLongOrNull(stmt, index++, data.getBid4Volume()); stmt.setBigDecimal(index++, data.getBid4Price());
            setLongOrNull(stmt, index++, data.getBid5Volume()); stmt.setBigDecimal(index++, data.getBid5Price());
            setLongOrNull(stmt, index++, data.getBid6Volume()); stmt.setBigDecimal(index++, data.getBid6Price());
            setLongOrNull(stmt, index++, data.getBid7Volume()); stmt.setBigDecimal(index++, data.getBid7Price());
            setLongOrNull(stmt, index++, data.getBid8Volume()); stmt.setBigDecimal(index++, data.getBid8Price());
            setLongOrNull(stmt, index++, data.getBid9Volume()); stmt.setBigDecimal(index++, data.getBid9Price());
            setLongOrNull(stmt, index++, data.getBid10Volume()); stmt.setBigDecimal(index++, data.getBid10Price());

            // 卖十档
            setLongOrNull(stmt, index++, data.getAsk1Volume()); stmt.setBigDecimal(index++, data.getAsk1Price());
            setLongOrNull(stmt, index++, data.getAsk2Volume()); stmt.setBigDecimal(index++, data.getAsk2Price());
            setLongOrNull(stmt, index++, data.getAsk3Volume()); stmt.setBigDecimal(index++, data.getAsk3Price());
            setLongOrNull(stmt, index++, data.getAsk4Volume()); stmt.setBigDecimal(index++, data.getAsk4Price());
            setLongOrNull(stmt, index++, data.getAsk5Volume()); stmt.setBigDecimal(index++, data.getAsk5Price());
            setLongOrNull(stmt, index++, data.getAsk6Volume()); stmt.setBigDecimal(index++, data.getAsk6Price());
            setLongOrNull(stmt, index++, data.getAsk7Volume()); stmt.setBigDecimal(index++, data.getAsk7Price());
            setLongOrNull(stmt, index++, data.getAsk8Volume()); stmt.setBigDecimal(index++, data.getAsk8Price());
            setLongOrNull(stmt, index++, data.getAsk9Volume()); stmt.setBigDecimal(index++, data.getAsk9Price());
            setLongOrNull(stmt, index++, data.getAsk10Volume()); stmt.setBigDecimal(index++, data.getAsk10Price());

            stmt.executeUpdate();
            logger.debug("保存十档盘口数据: {}", data.getSymbol());

        } catch (SQLException e) {
            logger.error("保存十档盘口数据失败: {}", data.getSymbol(), e);
            throw new RuntimeException("保存十档盘口数据失败", e);
        }
    }

    /**
     * 设置Long类型参数，处理null值
     */
    private void setLongOrNull(PreparedStatement stmt, int index, Long value) throws SQLException {
        if (value != null) {
            stmt.setLong(index, value);
        } else {
            stmt.setNull(index, Types.BIGINT);
        }
    }
}
