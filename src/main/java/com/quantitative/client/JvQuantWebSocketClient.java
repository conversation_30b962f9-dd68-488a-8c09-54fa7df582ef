package com.quantitative.client;

import com.quantitative.config.MarketConfig;
import com.quantitative.service.MarketDataService;
import org.java_websocket.client.WebSocketClient;
import org.java_websocket.handshake.ServerHandshake;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayOutputStream;
import java.net.URI;
import java.nio.ByteBuffer;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.zip.DataFormatException;
import java.util.zip.Inflater;

/**
 * jvQuant WebSocket客户端
 */
public class JvQuantWebSocketClient extends WebSocketClient {
    private static final Logger logger = LoggerFactory.getLogger(JvQuantWebSocketClient.class);
    private final SimpleDateFormat sdf = new SimpleDateFormat("HH:mm:ss");
    private final MarketConfig marketConfig;
    private final MarketDataService marketDataService;
    private final ScheduledExecutorService scheduler;
    
    private int reconnectAttempts = 0;
    private boolean isManualClose = false;

    public JvQuantWebSocketClient(String url, MarketDataService marketDataService) throws Exception {
        super(new URI(url));
        this.marketConfig = MarketConfig.getInstance();
        this.marketDataService = marketDataService;
        this.scheduler = Executors.newScheduledThreadPool(2);
        
        // 设置连接超时
        setConnectionLostTimeout(30);
        logger.info("WebSocket客户端初始化完成，服务器地址: {}", url);
    }

    @Override
    public void onOpen(ServerHandshake handshake) {
        logger.info("WebSocket连接已建立，状态码: {}", handshake.getHttpStatus());
        reconnectAttempts = 0;
        
        // 发送订阅命令
        String subscriptionCommand = marketConfig.generateSubscriptionCommand();
        if (!subscriptionCommand.equals("add=")) {
            this.send(subscriptionCommand);
            logger.info("发送订阅命令: {}", subscriptionCommand);
        } else {
            logger.warn("订阅命令为空，请检查配置");
        }
        
        // 启动心跳检测
        startHeartbeat();
    }

    @Override
    public void onMessage(String message) {
        logger.info("{} 文本响应: {}", sdf.format(new Date()), message);
        
        // 处理服务器响应消息
        if (message.contains("订阅成功") || message.contains("success")) {
            logger.info("订阅确认: {}", message);
        } else if (message.contains("错误") || message.contains("error")) {
            logger.error("服务器错误: {}", message);
        }
    }

    @Override
    public void onMessage(ByteBuffer bytes) {
        try {
            // 解压缩二进制数据
            byte[] decompressed = decompress(bytes.array());
            String data = new String(decompressed, "UTF-8");
            
            logger.debug("{} 二进制响应长度: {} 字节", sdf.format(new Date()), decompressed.length);
            
            // 交给业务服务处理
            marketDataService.processMarketData(data);
            
        } catch (Exception e) {
            logger.error("处理二进制消息失败", e);
        }
    }

    @Override
    public void onClose(int code, String reason, boolean remote) {
        logger.warn("WebSocket连接关闭，代码: {}, 原因: {}, 远程关闭: {}", code, reason, remote);
        
        // 停止心跳
        stopHeartbeat();
        
        // 如果不是手动关闭，尝试重连
        if (!isManualClose && reconnectAttempts < marketConfig.getMaxReconnectAttempts()) {
            scheduleReconnect();
        }
    }

    @Override
    public void onError(Exception ex) {
        logger.error("WebSocket连接异常", ex);
    }

    /**
     * 解压缩方法
     */
    private byte[] decompress(byte[] compressedData) throws DataFormatException {
        Inflater inflater = new Inflater(true);
        inflater.setInput(compressedData);
        
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream(compressedData.length);
        byte[] buffer = new byte[1024];
        
        while (!inflater.finished()) {
            int count = inflater.inflate(buffer);
            outputStream.write(buffer, 0, count);
        }
        
        inflater.end();
        return outputStream.toByteArray();
    }

    /**
     * 启动心跳检测
     */
    private void startHeartbeat() {
        scheduler.scheduleAtFixedRate(() -> {
            if (isOpen()) {
                send("ping");
                logger.debug("发送心跳包");
            }
        }, 30, 30, TimeUnit.SECONDS);
    }

    /**
     * 停止心跳检测
     */
    private void stopHeartbeat() {
        if (!scheduler.isShutdown()) {
            scheduler.shutdown();
        }
    }

    /**
     * 安排重连
     */
    private void scheduleReconnect() {
        reconnectAttempts++;
        long delay = marketConfig.getReconnectInterval();
        
        logger.info("安排第 {} 次重连，延迟 {} 毫秒", reconnectAttempts, delay);
        
        scheduler.schedule(() -> {
            try {
                logger.info("尝试重连...");
                this.reconnect();
            } catch (Exception e) {
                logger.error("重连失败", e);
                if (reconnectAttempts < marketConfig.getMaxReconnectAttempts()) {
                    scheduleReconnect();
                } else {
                    logger.error("达到最大重连次数，停止重连");
                }
            }
        }, delay, TimeUnit.MILLISECONDS);
    }

    /**
     * 手动关闭连接
     */
    public void closeManually() {
        isManualClose = true;
        this.close();
        stopHeartbeat();
        logger.info("手动关闭WebSocket连接");
    }

    /**
     * 获取连接状态
     */
    public boolean isConnected() {
        return isOpen();
    }

    /**
     * 发送自定义订阅命令
     */
    public void subscribe(String command) {
        if (isOpen()) {
            this.send(command);
            logger.info("发送自定义订阅命令: {}", command);
        } else {
            logger.warn("连接未建立，无法发送订阅命令");
        }
    }
}
