# 沪深股市数据收集系统

基于 jvQuant API 的沪深股市实时数据接收、解析和存储系统。

## 功能特性

- **实时数据接收**: 通过 WebSocket 连接 jvQuant 服务器接收实时行情数据
- **多种数据类型支持**: 
  - lv1 基础行情（五档盘口）
  - lv2 逐笔成交数据
  - lv10 十档盘口数据
- **数据解析**: 自动解压缩和解析二进制数据
- **数据存储**: 存储到 PostgreSQL 数据库
- **高性能处理**: 多线程异步处理，支持高频数据
- **自动重连**: WebSocket 连接断开自动重连
- **监控统计**: 实时处理统计和状态监控
- **控制台交互**: 支持命令行操作和状态查询

## 系统架构

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   jvQuant API   │───▶│  WebSocket客户端  │───▶│   数据解析器     │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                                         │
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│  PostgreSQL DB  │◀───│    数据访问层     │◀───│   业务服务层     │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

## 快速开始

### 1. 环境要求

- Java 11 或更高版本
- PostgreSQL 数据库
- Maven 3.6+

### 2. 配置数据库

创建 PostgreSQL 数据库：
```sql
CREATE DATABASE market_data;
```

### 3. 配置应用

编辑 `src/main/resources/application.properties`：

```properties
# jvQuant WebSocket配置
jvquant.websocket.url=ws://your-server-address/?token=your-token

# 订阅配置
jvquant.subscription.symbols=600519,000001,000002
jvquant.subscription.types=lv1,lv2,lv10

# PostgreSQL数据库配置
database.url=********************************************
database.username=postgres
database.password=your-password
```

### 4. 编译和运行

```bash
# 编译项目
mvn clean compile

# 运行应用
mvn exec:java -Dexec.mainClass="com.quantitative.Application"
```

## 数据库表结构

### lv1_market_data (基础行情)
- 股票基本信息（代码、名称、时间）
- 价格信息（最新价、涨幅）
- 成交信息（成交额、成交量）
- 买卖五档盘口数据

### lv2_tick_data (逐笔成交)
- 成交时间、成交编号
- 成交价格、成交数量

### lv10_depth_data (十档盘口)
- 股票基本信息
- 买卖十档盘口数据

## 控制台命令

运行系统后，可以使用以下命令：

- `help` (h) - 显示帮助信息
- `status` (s) - 显示系统状态
- `stats` - 显示处理统计
- `reconnect` (r) - 重新连接WebSocket
- `sub <cmd>` - 发送自定义订阅命令
- `quit` (q) - 退出系统

## 配置说明

### WebSocket 配置
- `jvquant.websocket.url`: jvQuant WebSocket 服务器地址
- `jvquant.websocket.reconnect.interval`: 重连间隔（毫秒）
- `jvquant.websocket.max.reconnect.attempts`: 最大重连次数

### 订阅配置
- `jvquant.subscription.symbols`: 订阅的股票代码列表
- `jvquant.subscription.types`: 订阅的数据类型（lv1,lv2,lv10）

### 数据库配置
- `database.url`: 数据库连接URL
- `database.username`: 数据库用户名
- `database.password`: 数据库密码
- `database.pool.*`: 连接池配置

## 性能优化

1. **多线程处理**: 使用线程池异步处理数据
2. **批量插入**: 逐笔成交数据批量插入数据库
3. **连接池**: 使用 HikariCP 高性能连接池
4. **队列缓冲**: 使用阻塞队列缓冲高频数据
5. **数据库索引**: 在时间和股票代码字段上创建索引

## 监控和日志

- 日志文件位置: `logs/` 目录
- 主日志: `market-data-collector.log`
- 错误日志: `error.log`
- 数据处理日志: `data-processing.log`

## 故障排除

### 常见问题

1. **WebSocket 连接失败**
   - 检查网络连接
   - 验证 token 是否正确
   - 确认服务器地址是否正确

2. **数据库连接失败**
   - 检查数据库服务是否启动
   - 验证连接参数是否正确
   - 确认数据库权限

3. **数据解析错误**
   - 检查数据格式是否符合预期
   - 查看解析器日志

## 扩展开发

### 添加新的数据类型
1. 在 `model` 包中创建新的数据模型
2. 在 `MarketDataParser` 中添加解析逻辑
3. 在 `MarketDataDao` 中添加存储方法
4. 更新数据库表结构

### 自定义数据处理
可以在 `MarketDataService` 中添加自定义的数据处理逻辑，如：
- 数据验证
- 实时计算
- 告警通知

## 许可证

MIT License
